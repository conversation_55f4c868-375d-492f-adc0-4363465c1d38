import { supabase } from '../lib/supabase';

class DatabaseService {
  // Get user data by auth_id
  async getUser(authId) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('auth_id', authId)
        .single();

      if (error) {
        console.error('Error fetching user:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getUser:', error);
      return null;
    }
  }

  // Get user transactions
  async getTransactions(authId, limit = 10) {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', authId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching transactions:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getTransactions:', error);
      return [];
    }
  }

  // Get user investments
  async getInvestments(authId) {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          *,
          investment_plans!inner(name, daily_return, total_return, duration_days)
        `)
        .eq('user_id', authId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching investments:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getInvestments:', error);
      return [];
    }
  }

  // Get referral data
  async getReferralData(authId) {
    try {
      // Get users who used this user's referral code
      const { data: referrals, error } = await supabase
        .from('users')
        .select('id, created_at, balance')
        .eq('referred_by', authId);

      if (error) {
        console.error('Error fetching referrals:', error);
        return {
          totalJoined: 0,
          referralEarn: 0
        };
      }

      const totalJoined = referrals?.length || 0;
      const referralEarn = referrals?.reduce((sum, ref) => sum + (parseFloat(ref.balance) * 0.05), 0) || 0;

      return {
        totalJoined,
        referralEarn
      };
    } catch (error) {
      console.error('Error in getReferralData:', error);
      return {
        totalJoined: 0,
        referralEarn: 0
      };
    }
  }

  // Update user balance
  async updateUserBalance(authId, balance, earnedFunds = 0, referralFunds = 0) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          balance: balance,
          earned_funds: earnedFunds,
          referral_funds: referralFunds,
          updated_at: new Date().toISOString()
        })
        .eq('auth_id', authId)
        .select()
        .single();

      if (error) {
        console.error('Error updating user balance:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in updateUserBalance:', error);
      return null;
    }
  }

  // Create a new transaction
  async createTransaction(authId, type, amount, status = 'completed', planName = null) {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .insert({
          user_id: authId,
          type: type,
          amount: amount,
          status: status,
          plan_name: planName,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating transaction:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in createTransaction:', error);
      return null;
    }
  }

  // Create a new investment
  async createInvestment(authId, planId, amount) {
    try {
      const { data, error } = await supabase
        .from('investments')
        .insert({
          user_id: authId,
          plan_id: planId,
          amount: amount,
          status: 'active',
          start_date: new Date().toISOString().split('T')[0],
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating investment:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in createInvestment:', error);
      return null;
    }
  }
}

export default new DatabaseService();
