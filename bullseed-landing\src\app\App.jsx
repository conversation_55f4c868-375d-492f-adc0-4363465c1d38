import React, { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { supabase } from '../lib/supabase';
// BullSeed App - Internal Dashboard
import ProtectedRoute from './components/ProtectedRoute';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import Dashboard from './pages/Dashboard';
import Deposit from './pages/Deposit';
import Withdraw from './pages/Withdraw';
import Invest from './pages/Invest';
import History from './pages/History';
import ActiveInvestments from './pages/ActiveInvestments';

import KYCStatus from './pages/KYCStatus';
import KYCApplication from './pages/KYCApplication';
import AdminLogin from './pages/AdminLogin';
import AdminDashboard from './pages/AdminDashboard';
import Profile from './pages/Profile';
import AccountSettings from './pages/AccountSettings';
import Referrals from './pages/Referrals';
import { UserPreferencesProvider } from '../contexts/UserPreferencesContext';
import './styles/App.css';

const App = () => {
  return (
    <Routes>
      {/* Admin Routes (not protected) */}
      <Route path="/admin/login" element={<AdminLogin />} />
      <Route path="/admin/dashboard" element={<AdminDashboard />} />

      {/* Main App Routes (protected) */}
      <Route path="/*" element={<MainApp />} />
    </Routes>
  );
};

const MainApp = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);

  // Function to refresh user profile
  const refreshUserProfile = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      const { data: profile, error } = await supabase
        .from('users')
        .select('*')
        .eq('auth_id', user.id)
        .single();

      if (profile) {
        console.log('App - Refreshing userProfile:', profile);
        setUserProfile(profile);
      } else if (error && error.code === 'PGRST116') {
        // Profile doesn't exist, trigger a full reload to create it
        console.log('App - Profile not found during refresh, reloading...');
        window.location.reload();
      }
    }
  };

  useEffect(() => {
    // Get current user and profile
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);

      if (user) {
        // Get user profile
        const { data: profile, error } = await supabase
          .from('users')
          .select('*')
          .eq('auth_id', user.id)
          .single();

        let userProfile;

        if (profile) {
          // User profile exists in database
          userProfile = profile;
        } else {
          // Profile doesn't exist, create one from auth user data
          const referralCode = 'BS' + Math.random().toString(36).substring(2, 10).toUpperCase();

          const newUserData = {
            auth_id: user.id,
            name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
            email: user.email,
            first_name: user.user_metadata?.first_name || user.user_metadata?.full_name?.split(' ')[0] || '',
            last_name: user.user_metadata?.last_name || user.user_metadata?.full_name?.split(' ')[1] || '',
            balance: 0,
            earned_funds: 0,
            referral_funds: 0,
            referral_code: referralCode,
            avatar_url: null,
            kyc_status: 'pending',
            language: 'en',
            timezone: 'America/New_York',
            currency: 'USD',
            email_notifications: true,
            sms_notifications: false,
            marketing_emails: true,
            login_alerts: true,
            two_factor_enabled: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          // Try to create the user profile in the database
          try {
            const { data: newProfile, error: createError } = await supabase
              .from('users')
              .insert(newUserData)
              .select();

            if (!createError && newProfile && newProfile.length > 0) {
              userProfile = newProfile[0];
              console.log('App - Created new user profile:', userProfile);
            } else {
              console.error('Failed to create user profile:', createError);
              // Use fallback profile
              userProfile = { ...newUserData, id: user.id };
            }
          } catch (createError) {
            console.error('Error creating user profile:', createError);
            // Use fallback profile if database insert fails
            userProfile = { ...newUserData, id: user.id };
          }
        }

        console.log('App - Setting userProfile:', userProfile);
        setUserProfile(userProfile);
      } else {
        console.log('App - No user found, clearing userProfile');
        setUserProfile(null);
      }
    };

    getCurrentUser();

    // Listen for custom balance update events
    const handleBalanceUpdate = () => {
      refreshUserProfile();
    };

    // Listen for profile update events
    const handleProfileUpdate = () => {
      refreshUserProfile();
    };

    window.addEventListener('userBalanceUpdated', handleBalanceUpdate);
    window.addEventListener('userProfileUpdated', handleProfileUpdate);

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user || null);
        if (!session?.user) {
          setUserProfile(null);
        }
        // Note: We're not fetching profile here, relying on the initial getCurrentUser call
      }
    );

    // Set up real-time subscription for user profile changes
    let userSubscription = null;
    if (user?.id) {
      userSubscription = supabase
        .channel('user_profile_changes')
        .on('postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'users',
            filter: `auth_id=eq.${user.id}`
          },
          (payload) => {
            console.log('App - User profile updated:', payload);
            // Update user profile with new data
            if (payload.new) {
              setUserProfile(payload.new);
            }
          }
        )
        .subscribe();
    }

    return () => {
      subscription.unsubscribe();
      if (userSubscription) {
        userSubscription.unsubscribe();
      }
      window.removeEventListener('userBalanceUpdated', handleBalanceUpdate);
      window.removeEventListener('userProfileUpdated', handleProfileUpdate);
    };
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleUserUpdate = (updatedUser) => {
    console.log('App - User profile updated:', updatedUser);
    setUserProfile(updatedUser);
  };

  return (
    <ProtectedRoute>
      <UserPreferencesProvider>
        <div className="app">
          <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />
          <div className={`app-main ${sidebarOpen ? 'sidebar-open' : ''}`}>
            <Header user={userProfile} onMenuClick={toggleSidebar} onUserUpdate={handleUserUpdate} />
            <main className="app-content">
              <Routes>
                <Route path="/" element={<Dashboard user={userProfile} />} />
                <Route path="/dashboard" element={<Dashboard user={userProfile} />} />
                <Route path="/deposit" element={<Deposit user={userProfile} />} />
                <Route path="/withdraw" element={<Withdraw user={userProfile} />} />
                <Route path="/invest" element={<Invest user={userProfile} />} />
                <Route path="/history" element={<History user={userProfile} />} />
                <Route path="/active-investments" element={<ActiveInvestments user={userProfile} />} />

                <Route path="/kyc-status" element={<KYCStatus user={userProfile} />} />
                <Route path="/kyc-application" element={<KYCApplication user={userProfile} />} />
                <Route path="/profile" element={<Profile user={userProfile} />} />
                <Route path="/account-settings" element={<AccountSettings user={userProfile} />} />
                <Route path="/referrals" element={<Referrals user={userProfile} />} />
              </Routes>
            </main>
          </div>
        </div>
      </UserPreferencesProvider>
    </ProtectedRoute>
  );
};

export default App;
