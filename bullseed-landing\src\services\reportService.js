import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

class ReportService {
  constructor() {
    this.companyInfo = {
      name: 'BullSeed',
      address: 'Crypto Investment Platform',
      website: 'www.bullseed.com',
      email: '<EMAIL>'
    };
  }

  // Generate PDF report for user transactions and investments
  async generateUserReport(user, transactions = [], investments = [], marketData = []) {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.width;
    const pageHeight = pdf.internal.pageSize.height;
    let yPosition = 20;

    // Helper function to add new page if needed
    const checkPageBreak = (requiredSpace = 20) => {
      if (yPosition + requiredSpace > pageHeight - 20) {
        pdf.addPage();
        yPosition = 20;
        return true;
      }
      return false;
    };

    // Header with logo
    pdf.setFontSize(28);
    pdf.setTextColor(16, 185, 129); // BullSeed green
    pdf.text(this.companyInfo.name, 20, yPosition);

    pdf.setFontSize(12);
    pdf.setTextColor(100, 100, 100);
    pdf.text('Crypto Investment Platform - Investment Report', 20, yPosition + 10);
    
    // Date
    pdf.setTextColor(0, 0, 0);
    const reportDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    pdf.text(`Generated on: ${reportDate}`, pageWidth - 80, yPosition);
    
    yPosition += 30;

    // User Information
    checkPageBreak(40);
    pdf.setFontSize(16);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Account Summary', 20, yPosition);
    yPosition += 15;

    pdf.setFontSize(12);
    pdf.text(`Name: ${user.name || 'N/A'}`, 20, yPosition);
    yPosition += 8;
    pdf.text(`Email: ${user.email || 'N/A'}`, 20, yPosition);
    yPosition += 8;
    pdf.text(`Available Balance: $${(user.balance || 0).toFixed(2)}`, 20, yPosition);
    yPosition += 8;
    pdf.text(`Earned Funds: $${(user.earned_funds || user.earnedFunds || 0).toFixed(2)}`, 20, yPosition);
    yPosition += 8;
    pdf.text(`Referral Funds: $${(user.referral_funds || user.referralFunds || 0).toFixed(2)}`, 20, yPosition);
    yPosition += 20;

    // Market Overview
    if (marketData.length > 0) {
      checkPageBreak(60);
      pdf.setFontSize(16);
      pdf.text('Market Overview', 20, yPosition);
      yPosition += 15;

      pdf.setFontSize(10);
      pdf.text('Symbol', 20, yPosition);
      pdf.text('Price', 80, yPosition);
      pdf.text('24h Change', 130, yPosition);
      pdf.text('Volume', 170, yPosition);
      yPosition += 8;

      // Draw line
      pdf.line(20, yPosition, pageWidth - 20, yPosition);
      yPosition += 5;

      marketData.slice(0, 4).forEach(coin => {
        checkPageBreak();
        pdf.text(coin.symbol || 'N/A', 20, yPosition);

        const price = coin.price || coin.current_price || 0;
        pdf.text(`$${typeof price === 'number' ? price.toFixed(2) : price}`, 80, yPosition);

        const change = coin.change || coin.price_change_percentage_24h || 0;
        const changeColor = change >= 0 ? [0, 128, 0] : [255, 0, 0];
        pdf.setTextColor(...changeColor);
        pdf.text(`${change >= 0 ? '+' : ''}${change.toFixed(2)}%`, 130, yPosition);
        pdf.setTextColor(0, 0, 0);

        pdf.text(coin.volume || 'N/A', 170, yPosition);
        yPosition += 8;
      });
      yPosition += 15;
    }

    // Transaction History
    if (transactions.length > 0) {
      checkPageBreak(60);
      pdf.setFontSize(16);
      pdf.text('Transaction History', 20, yPosition);
      yPosition += 15;

      pdf.setFontSize(10);
      pdf.text('Date', 20, yPosition);
      pdf.text('Type', 60, yPosition);
      pdf.text('Amount', 100, yPosition);
      pdf.text('Status', 140, yPosition);
      pdf.text('Description', 170, yPosition);
      yPosition += 8;

      // Draw line
      pdf.line(20, yPosition, pageWidth - 20, yPosition);
      yPosition += 5;

      transactions.forEach(transaction => {
        checkPageBreak();
        const date = transaction.created_at ? new Date(transaction.created_at).toLocaleDateString() : 'N/A';
        pdf.text(date, 20, yPosition);
        pdf.text((transaction.type || 'transaction').toUpperCase(), 60, yPosition);
        pdf.text(`$${(transaction.amount || 0).toFixed(2)}`, 100, yPosition);

        const status = transaction.status || 'unknown';
        const statusColor = status === 'completed' ? [0, 128, 0] :
                           status === 'pending' ? [255, 165, 0] : [255, 0, 0];
        pdf.setTextColor(...statusColor);
        pdf.text(status.toUpperCase(), 140, yPosition);
        pdf.setTextColor(0, 0, 0);

        const description = transaction.description || `${transaction.type || 'transaction'} transaction`;
        pdf.text(description.substring(0, 25), 170, yPosition);
        yPosition += 8;
      });
      yPosition += 15;
    }

    // Investment Summary
    if (investments.length > 0) {
      checkPageBreak(60);
      pdf.setFontSize(16);
      pdf.text('Active Investments', 20, yPosition);
      yPosition += 15;

      pdf.setFontSize(10);
      pdf.text('Plan', 20, yPosition);
      pdf.text('Amount', 70, yPosition);
      pdf.text('Daily Return', 110, yPosition);
      pdf.text('Total Earned', 150, yPosition);
      pdf.text('Status', 190, yPosition);
      yPosition += 8;

      // Draw line
      pdf.line(20, yPosition, pageWidth - 20, yPosition);
      yPosition += 5;

      investments.forEach(investment => {
        checkPageBreak();
        pdf.text(investment.plan_name || 'Investment Plan', 20, yPosition);
        pdf.text(`$${(investment.amount || 0).toFixed(2)}`, 70, yPosition);
        pdf.text(`$${(investment.daily_return || 0).toFixed(2)}`, 110, yPosition);
        pdf.text(`$${(investment.total_earned || 0).toFixed(2)}`, 150, yPosition);

        const status = investment.status || 'unknown';
        const statusColor = status === 'active' ? [0, 128, 0] : [128, 128, 128];
        pdf.setTextColor(...statusColor);
        pdf.text(status.toUpperCase(), 190, yPosition);
        pdf.setTextColor(0, 0, 0);

        yPosition += 8;
      });
      yPosition += 15;
    }

    // Footer
    const totalPages = pdf.internal.getNumberOfPages();
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setTextColor(128, 128, 128);
      pdf.text(`${this.companyInfo.name} - ${this.companyInfo.website}`, 20, pageHeight - 10);
      pdf.text(`Page ${i} of ${totalPages}`, pageWidth - 40, pageHeight - 10);
    }

    return pdf;
  }

  // Download the PDF report
  async downloadReport(user, transactions = [], investments = [], marketData = []) {
    try {
      // Validate user object
      if (!user) {
        throw new Error('User data is required');
      }

      console.log('Report generation data:', {
        user: {
          name: user.name,
          email: user.email,
          balance: user.balance
        },
        transactionCount: transactions.length,
        investmentCount: investments.length,
        marketDataCount: marketData.length
      });

      const pdf = await this.generateUserReport(user, transactions, investments, marketData);
      const userName = (user.name || 'User').replace(/\s+/g, '_');
      const fileName = `BullSeed_Report_${userName}_${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);
      return { success: true, fileName };
    } catch (error) {
      console.error('Error generating report:', error);
      return { success: false, error: error.message };
    }
  }

  // Generate CSV export for transactions
  generateTransactionCSV(transactions) {
    const headers = ['Date', 'Type', 'Amount', 'Status', 'Currency', 'Description'];
    const csvContent = [
      headers.join(','),
      ...transactions.map(transaction => [
        new Date(transaction.created_at).toLocaleDateString(),
        transaction.type,
        transaction.amount,
        transaction.status,
        transaction.crypto_currency || 'USD',
        `"${transaction.description || ''}"`
      ].join(','))
    ].join('\n');

    return csvContent;
  }

  // Download CSV file
  downloadCSV(transactions, filename = 'transactions.csv') {
    const csvContent = this.generateTransactionCSV(transactions);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  // Generate summary statistics
  generateSummaryStats(transactions, investments) {
    const stats = {
      totalDeposits: 0,
      totalWithdrawals: 0,
      totalInvestments: 0,
      totalEarnings: 0,
      transactionCount: transactions.length,
      investmentCount: investments.length
    };

    transactions.forEach(transaction => {
      switch (transaction.type) {
        case 'deposit':
          stats.totalDeposits += transaction.amount;
          break;
        case 'withdrawal':
          stats.totalWithdrawals += transaction.amount;
          break;
        case 'investment':
          stats.totalInvestments += transaction.amount;
          break;
        case 'earning':
          stats.totalEarnings += transaction.amount;
          break;
      }
    });

    return stats;
  }
}

export default new ReportService();
